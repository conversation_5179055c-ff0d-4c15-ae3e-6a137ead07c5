
NEXT_PUBLIC_DEBUG=
NEXT_PUBLIC_APP_URL=https://vibany.com

#  vercel secret key and cron timeout
CRON_SECRET=
TIMEOUT_SECONDS=790

# Neon or Supabase
DATABASE_URL="postgresql://"

# Clerk
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_
CLERK_SECRET_KEY=sk_
CLERK_SIGN_IN_FALLBACK_REDIRECT_URL=/draw
CLERK_SIGN_UP_FALLBACK_REDIRECT_URL=/draw

# dulupay Payment switcher
NEXT_PUBLIC_WXPAY_DISABLED=
NEXT_PUBLIC_ALIPAY_DISABLED=

# dulupay Payment config
# 查看地址 https://www.dulupay.com/user/userinfo.php?mod=api
PAY_API_URL=https://api.dulupay.com
# 商户 ID
PAY_PID=
# 平台公钥
PAY_PUBLIC_KEY=
# 商户公钥
PAY_MERCHANT_PRIVATE_KEY=

# STRIPE
NEXT_PUBLIC_STRIPE_DISABLED=on
STRIPE_SECRET_KEY=sk-
STRIPE_WEBHOOK_SECRET=sk-

# Tuzi API Key
TUZI_API_URL=https://api.tu-zi.com/v1
TUZI_API_KEY=sk-
TUZI_MODEL_IMAGE=gpt-4o-image
TUZI_MODEL_IMAGE_VIP=gpt-4o-image-vip
TUZI_MODEL_IMAGE_SMALL=gpt-4o-image

# XAI API Key
XAI_API_URL=https://api.xai.com/v1
XAI_API_KEY=xai-
XAI_API_MODEL_IMAGE=grok-2-image-latest

# Get your OpenAI API Key here for chat models: https://platform.openai.com/account/api-keys
OPENAI_API_URL=https://api.openai.com/v1
OPENAI_API_KEY=sk-
OPENAI_MODEL_IMAGE=gpt-image-1
OPENAI_MODEL_IMAGE_SMALL=dall-e-3

# DeepSeek API Key
# https://www.volcengine.com/experience/ark?utm_term=202502dsinvite&ac=DSASUQY5&rc=S5JCRGKP

OPENAI_COMPLETIONS_BASE_URL=https://ark.cn-beijing.volces.com/api/v3

# DeepSeek API Key
OPENAI_COMPLETIONS_API_KEY=

# DeepSeek Model
OPENAI_COMPLETIONS_MODEL_SMALL=doubao-1-5-lite-32k-250115
OPENAI_COMPLETIONS_MODEL_LARGE=deepseek-v3-250324
OPENAI_COMPLETIONS_MODEL_REASONING=deepseek-r1-250120
OPENAI_COMPLETIONS_MODEL_FUNCTION=doubao-pro-32k-functioncall-241028

# Cloudflare R2 Storage
R2_ACCOUNT_ID=1
R2_ACCESS_KEY_ID=1
R2_SECRET_ACCESS_KEY=1
R2_BUCKET_NAME=vibany
R2_PUBLIC_URL_PREFIX=https://cdn-dev.images.zhaikr.com
