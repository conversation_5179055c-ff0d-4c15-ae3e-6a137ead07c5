import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { histories, History } from "@/lib/db/schema";
import { and, not, inArray, gte } from "drizzle-orm";
import { drawModels } from "@/constants/draw/models";
import { createLogger } from "@/lib/draw/logger";

const logger = createLogger('api-public-status');

export const dynamic = 'force-dynamic';

export async function GET() {
  try {
    // 获取指定小时数之前的时间
    const previousHours = process.env.NEXT_PUBLIC_DEBUG === "on" ? 160 : 12;

    const hoursAgo = new Date();
    hoursAgo.setHours(hoursAgo.getHours() - previousHours);

    // 获取所有非禁用的模型ID
    const enabledModelIds = drawModels
      .filter(model => !model.disabled)
      .map(model => model.id);

    const completedHistories = await db.query.histories.findMany({
      where: and(
        gte(histories.updatedAt, hoursAgo),
        not(inArray(histories.drawStatus, ['PENDING', 'PROCESSING'])),
      ),
    }) as History[];

    // 按模型分组并计算成功率
    const modelStats = enabledModelIds.reduce((acc, modelId) => {
      // 过滤出当前模型的历史记录
      const modelHistories = completedHistories.filter(
        history => history.extra?.model === modelId
      );

      // 计算成功率
      const total = modelHistories.length;
      const successful = modelHistories.filter(
        history => history.status && history.drawStatus === 'SUCCESS'
      ).length;

      // 如果没有数据，默认为 0%，而不是 100%
      const successRate = total > 0 ? (successful / total) * 100 : -1;

      // 添加到结果中，只返回必要的信息，不暴露具体的成功和总数
      acc[modelId] = {
        id: modelId,
        name: drawModels.find(m => m.id === modelId)?.name || modelId,
        successRate: Math.round(successRate) // 四舍五入到整数
      };

      // 记录日志，方便调试
      // logger.debug(`Model ${modelId} stats:`, { total, successful, successRate });

      return acc;
    }, {} as Record<string, {
      id: string;
      name: string;
      successRate: number;
    }>);

    return NextResponse.json({
      timestamp: new Date().toISOString(),
      models: modelStats
    });
  } catch (error) {
    logger.error("Error fetching model status", error instanceof Error ? error : new Error(String(error)));
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
