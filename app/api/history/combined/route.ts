import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { histories } from "@/lib/db/schema";
import { eq, desc, and, not, or, gte } from "drizzle-orm";
import { createLogger } from "@/lib/draw/logger";

const logger = createLogger('api-histories-combined');

export async function GET(req: Request) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const includeShare = searchParams.get("includeShare") === "true";
    const recentLimit = parseInt(searchParams.get("recentLimit") || "5");
    const pendingLimit = parseInt(searchParams.get("pendingLimit") || "6");
    const pendingHours = parseInt(searchParams.get("pendingHours") || "24");

    logger.debug('Fetching combined histories', {
      userId,
      includeShare,
      recentLimit,
      pendingLimit,
      pendingHours
    });

    // Get recent successful histories
    const recentHistories = await db.query.histories.findMany({
      where: and(
        eq(histories.userId, userId),
        eq(histories.status, true),
        eq(histories.archived, false)
      ),
      orderBy: [desc(histories.createdAt)],
      limit: recentLimit,
      with: includeShare ? {
        share: {
          columns: {
            id: true,
            shareId: true,
            isPublic: true,
            allowFork: true,
            viewCount: true,
            likeCount: true,
            forkCount: true,
          }
        }
      } : undefined,
    });

    // Get pending histories (drawStatus not SUCCESS or status is false) within the specified time range
    // Default to last 24 hours if not specified
    const pendingTimeThreshold = new Date();
    pendingTimeThreshold.setHours(pendingTimeThreshold.getHours() - pendingHours);

    const pendingHistories = await db.query.histories.findMany({
      where: and(
        eq(histories.userId, userId),
        eq(histories.archived, false),
        or(
          not(eq(histories.drawStatus, 'SUCCESS')),
          eq(histories.status, false)
        ),
        // Only include histories created within the specified time range
        gte(histories.createdAt, pendingTimeThreshold)
      ),
      orderBy: [desc(histories.createdAt)],
      limit: pendingLimit,
      with: includeShare ? {
        share: {
          columns: {
            id: true,
            shareId: true,
            isPublic: true,
            allowFork: true,
            viewCount: true,
            likeCount: true,
            forkCount: true,
          }
        }
      } : undefined,
    });

    logger.debug('Combined histories fetched', {
      recentHistoriesCount: recentHistories.length,
      pendingHistoriesCount: pendingHistories.length,
      pendingTimeThreshold: pendingTimeThreshold.toISOString()
    });

    return NextResponse.json({
      recentHistories,
      pendingHistories,
    });
  } catch (error) {
    logger.error("Error fetching combined histories", error instanceof Error ? error : new Error(String(error)));
    return new NextResponse("Internal Error", { status: 500 });
  }
}
