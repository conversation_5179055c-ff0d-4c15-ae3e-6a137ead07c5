import "@/app/globals.css";
import "react-day-picker/style.css";

import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";

import Script from "next/script";
import { Metadata } from "next";

import { UserInitializer } from "@/components/global/user-initializer";
import { Toaster } from "@/components/ui/toaster";
import { getBaseMetadata } from "@/constants";

export const metadata: Metadata = getBaseMetadata();

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <Script id="microsoft-clarity" strategy="afterInteractive">
          {`
            (function(c,l,a,r,i,t,y){
                c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
            })(window, document, "clarity", "script", "r3ryf8ho8s");
          `}
        </Script>
      </head>
      <body id="root" className="min-h-screen antialiased sm:pt-16">
        <ClerkProvider>
          <UserInitializer />
          {children}
          <Toaster />
        </ClerkProvider>
      </body>
    </html>
  );
}
