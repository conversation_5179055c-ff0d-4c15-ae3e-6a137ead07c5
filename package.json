{"name": "vibany-image-ai-render", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "compile": "next build", "build": "npm run db:update && npm run compile", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate", "db:push": "drizzle-kit push", "db:update": "npm run db:generate && npm run db:push", "db:studio": "drizzle-kit studio", "db:migrate": "tsx lib/db/migrate.ts"}, "dependencies": {"@ai-sdk/openai": "^1.3.16", "@ai-sdk/openai-compatible": "^0.1.17", "@ai-sdk/react": "^1.1.20", "@ai-sdk/xai": "^1.2.13", "@aws-sdk/client-s3": "^3.787.0", "@aws-sdk/s3-request-presigner": "^3.787.0", "@clerk/nextjs": "6.9.12", "@hookform/resolvers": "^5.0.1", "@jsquash/webp": "^1.4.0", "@neondatabase/serverless": "^1.0.0", "@radix-ui/react-accordion": "^1.2.7", "@radix-ui/react-alert-dialog": "^1.1.10", "@radix-ui/react-avatar": "^1.1.6", "@radix-ui/react-checkbox": "^1.2.2", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.11", "@radix-ui/react-hover-card": "^1.1.10", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-navigation-menu": "^1.2.9", "@radix-ui/react-popover": "^1.1.10", "@radix-ui/react-scroll-area": "^1.2.5", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.8", "@radix-ui/react-toast": "^1.2.10", "@radix-ui/react-tooltip": "^1.2.3", "@re-dev/react-truncate": "^0.5.1", "@types/lodash": "^4.17.16", "@types/marked": "^5.0.2", "@types/node": "^22.14.1", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "^10.0.0", "ai": "^4.3.9", "air-datepicker": "^3.5.3", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clipboard": "^2.0.11", "clsx": "^2.1.1", "date-fns": "^4.1.0", "drizzle-orm": "^0.41.0", "es-toolkit": "^1.35.0", "framer-motion": "^12.7.4", "gsap": "^3.12.7", "lodash": "^4.17.21", "lucide-react": "^0.477.0", "marked": "^15.0.8", "next": "^15.3.1", "next-themes": "^0.4.6", "postcss": "^8.5.3", "postgres": "^3.4.5", "prism-react-renderer": "^2.4.1", "qrcode.react": "^4.2.0", "react": "^19.1.0", "react-colorful": "^5.6.1", "react-day-picker": "^9.6.7", "react-dom": "^19.1.0", "react-hook-form": "^7.56.0", "recharts": "^2.15.3", "stripe": "^18.0.0", "tailwind-merge": "^3.2.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "^5.8.3", "uuid": "^11.1.0", "zod": "^3.24.3", "zustand": "^5.0.3"}, "devDependencies": {"@iconify/react": "^5.2.1", "@tailwindcss/typography": "^0.5.16", "dotenv": "^16.5.0", "dotenv-cli": "^8.0.0", "drizzle-kit": "^0.31.0", "eslint": "9.23.0", "eslint-config-next": "15.2.4", "tsx": "^4.19.3"}}