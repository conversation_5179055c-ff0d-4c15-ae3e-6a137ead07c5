"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import Image from "next/image";
import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu"
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card"
import { PRICING_TIERS, NAV_ITEMS } from "@/constants";
import { SiteLogo } from "@/components/global/site-logo";

interface NavItem {
  href: string;
  label: string;
  icon: string;
  key: string;
}

const navItems: NavItem[] = NAV_ITEMS;

export function MainNav() {
  const pathname = usePathname();

  const isActive = (href: string) => {
    if (href === "/") {
      return pathname === href;
    }
    if (href === "/settings/history") {
      return pathname?.startsWith("/settings");
    }
    return pathname?.startsWith(href) &&
      (pathname === href || pathname.startsWith(`${href}/`));
  };

  return (
    <NavigationMenu className="w-full">
      <NavigationMenuList className="flex flex-wrap items-center gap-0.5 md:gap-1">
        <div className="md:hidden mr-2 pb-0.5 pl-2">
          <NavigationMenuItem>
            <SiteLogo />
          </NavigationMenuItem>
        </div>
        {navItems.map((item) => (
          <NavigationMenuItem key={item.href} className="flex-shrink-0">
            {item.key === "pricing" ? (
              <HoverCard>
                <HoverCardTrigger asChild>
                  <NavigationMenuLink asChild>
                    <Link
                      href={item.href}
                      className={cn(
                        navigationMenuTriggerStyle(),
                        "px-2 md:px-4 min-w-0 whitespace-nowrap",
                        isActive(item.href) && "bg-accent text-accent-foreground"
                      )}
                    >
                      {item.icon} {item.label}
                    </Link>
                  </NavigationMenuLink>
                </HoverCardTrigger>
                <HoverCardContent className="w-80 max-w-[calc(100vw-2rem)] backdrop-blur-sm bg-background border-border/70">
                  <div className="space-y-4">
                    {PRICING_TIERS.map((tier) => (
                      <div
                        key={tier.id}
                        className="space-y-2 cursor-pointer hover:bg-accent/50 p-2 rounded-md transition-colors"
                        onClick={() => {
                          window.location.href =
                            tier.price === 0 ? "/draw" : "/pricing";
                        }}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2 min-w-0">
                            <span className="flex-shrink-0">{tier.icon}</span>
                            <span className="font-medium truncate">{tier.name}</span>
                          </div>
                          <span className="text-sm text-muted-foreground flex-shrink-0">
                            {tier.price === 0 ? "免费" : `¥${tier.price}`}
                          </span>
                        </div>
                        <p className="text-sm text-muted-foreground break-words">
                          {tier.description}
                        </p>
                      </div>
                    ))}
                  </div>
                </HoverCardContent>
              </HoverCard>
            ) : item.key === "contact" ? (
              <HoverCard>
                <HoverCardTrigger asChild>
                  <NavigationMenuLink asChild>
                    <Link
                      href={item.href}
                      className={cn(
                        navigationMenuTriggerStyle(),
                        "px-2 md:px-4 min-w-0 whitespace-nowrap",
                        isActive(item.href) && "bg-accent text-accent-foreground"
                      )}
                    >
                      {item.icon} {item.label}
                    </Link>
                  </NavigationMenuLink>
                </HoverCardTrigger>
                <HoverCardContent className="w-80 max-w-[calc(100vw-2rem)] backdrop-blur-sm bg-background border-border/70">
                  <div className="space-y-4">
                    <div className="flex flex-col items-center gap-4">
                      <h3 className="text-sm font-semibold">加入微信群</h3>
                      <div className="p-2 bg-[#FAFAFB] rounded-lg">
                        <Image
                          src="/images/wx_group.png"
                          alt="WeChat Group QR Code"
                          height={200}
                          width={200}
                          className="rounded-lg"
                        />
                      </div>
                    </div>
                  </div>
                </HoverCardContent>
              </HoverCard>
            ) : (
              <NavigationMenuLink asChild>
                <Link
                  href={item.href}
                  className={cn(
                    navigationMenuTriggerStyle(),
                    "px-2 md:px-4 min-w-0 whitespace-nowrap",
                    isActive(item.href) && "bg-accent text-accent-foreground"
                  )}
                >
                  {item.icon} {item.label}
                </Link>
              </NavigationMenuLink>
            )}
          </NavigationMenuItem>
        ))}
      </NavigationMenuList>
    </NavigationMenu>
  );
}
