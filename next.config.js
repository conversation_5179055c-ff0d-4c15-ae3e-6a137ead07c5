/** @type {import('next').NextConfig} */
const nextConfig = {
  allowedDevOrigins: [
    ...(process.env.NEXT_PUBLIC_DEBUG === "on"
      ? [
          "localhost",
          "*.localhost",
          "127.0.0.1",
          "local-origin.dev",
          "*.local-origin.dev",
        ]
      : []),
  ],
  images: {
    // remotePatterns 用于配置允许通过 Next.js Image 组件加载的外部图片域名
    remotePatterns: [
      {
        protocol: "https",
        hostname: "videoopenai.1984871009.workers.dev",
        port: "",
      },
      {
        protocol: "http",
        hostname: "videoopenai.1984871009.workers.dev",
        port: "",
      },
      {
        protocol: "https",
        hostname: "images.unsplash.com",
        port: "",
      },
      {
        protocol: "https",
        hostname: "plus.unsplash.com",
        port: "",
      },
      {
        protocol: "https",
        hostname: "cdn.zhaikr.com",
        port: "",
      },
      {
        protocol: "https",
        hostname: "cdn.vibany.com",
        port: "",
      },
      {
        protocol: "https",
        hostname: "cdn.images.zhaikr.com",
        port: "",
      },
      {
        protocol: "https",
        hostname: "cdn-dev.images.zhaikr.com",
        port: "",
      },
    ],
  },
};

module.exports = nextConfig;
