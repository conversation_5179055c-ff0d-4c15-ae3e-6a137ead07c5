export interface ChatModel {
  id: string;
  name: string;
  description: string;
  paidOnly: boolean;
  disabled: boolean;
  points: number;
  maxTokens?: number;
}

export const DEFAULT_CHAT_MODEL = "chat-model-large";
export const VIP_DEFAULT_CHAT_MODEL = "chat-model-large";

export const DEBUG_MODE = process.env.NEXT_PUBLIC_DEBUG === "on";

export const chatModels: Array<ChatModel> = [
  {
    id: "chat-model-small",
    name: "基础模型",
    description: "基于豆包 1.5 Lite，适合简单提示词生成",
    paidOnly: false,
    disabled: false,
    points: 0,
    maxTokens: 4000,
  },
  {
    id: "chat-model-large",
    name: "高级模型",
    description: "基于 Deepseek v3，快速稳定高质量",
    paidOnly: false,
    disabled: false,
    points: 0,
    maxTokens: 8000,
  },
  {
    id: "chat-model-reasoning",
    name: "创意模型",
    description: "基于 Deepseek R1，更具创意、更高质量",
    paidOnly: true,
    disabled: false,
    points: 0,
    maxTokens: 8000,
  },
];
