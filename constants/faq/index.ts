export const faqItems = [
  {
    id: "item-1",
    question: "这用的是什么模型？",
    answer:
      "现在绘图使用的是 ChatGPT 4o 以及 gpt-image-1 模型，提示词 AI 辅助生成使用的是 Deepseek v3/R2 以及豆包 1.5 Lite 等模型。",
  },
  {
    id: "item-2",
    question: "不同的模型有什么区别？",
    answer:
      "模型分为两组，一组是基于 ChatGPT 4o 页面的模型，另一组是基于 OpenAI gpt-image-1 API 的最新生图模型。每组模型不同渠道的资源、性能和稳定性不同，所以也反映到了价格上。",
  },
  {
    id: "item-3",
    question: "网站是免费的吗？",
    answer:
      "由于每次制图都要支付给服务商 api 的费用，所以为了保证长期稳定的运营，网站是收费的。但是我们给每个新注册的用户准备了免费试用的积分",
  },
  {
    id: "item-4",
    question: "怎么使用？",
    answer: "先选择风格，后上传图片或填写提示词，点击生成后即可开始",
  },
  {
    id: "item-5",
    question: "生成图片怎么这么慢？",
    answer:
      "目前生成一张图大概需要 1-5 分钟左右，也有一定概率失败，这跟 ChatGPT 官方体验以及上游负载有关，请耐心等待。",
  },
  {
    id: "item-6",
    question: "生成图片失败了怎么办？",
    answer:
      "生成失败不会扣积分，可以点击再次生成来重试，如果系统误扣积分，请联系客服处理",
  },
  {
    id: "item-7",
    question: "怎样查看历史记录？",
    answer:
      "点击导航栏顶部的'我的'即可查看历史生成的图片，如果比较满意的图片请一定要注意保存备份",
  },
  {
    id: "item-8",
    question: "参考图片和提示词需要同时提供吗？",
    answer:
      "参考图片和提示词是二选一关系，只需要有一项即可生成图片，但有些情况如果同时提供可以更加精确控制生成效果",
  },
  {
    id: "item-9",
    question: "可以上传多少张参考图片？",
    answer:
      "满血版最多可以上传 10 张参考图片，其他不同渠道和模型能力不同，具体请查看模型介绍",
  },
  {
    id: "item-10",
    question: "风格提示词不满意怎么办？",
    answer:
      "如果风格提示词不满意，可以考虑使用默认风格加自定义提示词来获得更好的效果",
  },
  {
    id: "item-11",
    question: "模型为什么有时候质量突然下降很多？",
    answer:
      "这是由 ChatGPT 官方的模型降智导致的，我们无法控制，但是目前已经识别了这种情况，比如生成了图片，但是是 webp 格式，会免除这次生成的积分消耗",
  },
];
