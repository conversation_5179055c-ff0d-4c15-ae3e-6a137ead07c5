import { uploadBuffer } from "@/lib/storage/r2-client";

/**
 * 处理 type: "image" 模型返回的 base64 图片数据
 * 将其上传到 R2 存储并返回公共 URL
 *
 * @param imageData base64 格式的图片数据
 * @param userId 用户 ID
 * @param historyId 历史记录 ID
 * @returns 上传后的图片 URL
 */
export async function processModelImage(
  imageData: string,
  userId: string,
  historyId: string,
  imageType?: string,
): Promise<string> {
  // 将 base64 数据转换为 Buffer
  const buffer = Buffer.from(imageData, "base64");

  // 从 base64 数据中推断内容类型
  // 注意：OpenAI 的 gpt-image-1 模型目前返回的是 PNG 格式
  // 但为了代码的可维护性，我们尝试从数据中推断格式
  const contentType = imageType || inferContentTypeFromBase64(imageData);

  // 根据内容类型确定文件扩展名
  const extension = getExtensionFromContentType(contentType);

  // 生成唯一的文件名
  const filename = `image-${Date.now()}.${extension}`;

  // 构建 R2 存储路径
  const r2Key = `${userId}/${historyId}/${filename}`;

  // 上传到 R2 存储
  const r2Url = await uploadBuffer(buffer, r2Key, contentType);

  return r2Url;
}

/**
 * 从 base64 数据中推断内容类型
 *
 * @param base64Data base64 格式的数据
 * @returns 推断的内容类型
 */
function inferContentTypeFromBase64(base64Data: string): string {
  // 默认为 PNG 格式，因为 OpenAI 的 gpt-image-1 模型目前返回的是 PNG
  let contentType = "image/png";

  // 尝试从 base64 数据的前几个字节推断格式
  // 这是一个简化的实现，实际上可以通过检查 base64 解码后的二进制头部来更准确地判断
  // 但由于我们知道 OpenAI 返回的是 PNG，这里主要是为了代码的可扩展性

  // 如果将来需要支持更多格式，可以在这里添加更复杂的检测逻辑

  return contentType;
}

/**
 * 根据内容类型获取文件扩展名
 *
 * @param contentType 内容类型
 * @returns 文件扩展名
 */
function getExtensionFromContentType(contentType: string): string {
  switch (contentType) {
    case "image/png":
      return "png";
    case "image/jpeg":
      return "jpg";
    case "image/webp":
      return "webp";
    case "image/gif":
      return "gif";
    default:
      return "png"; // 默认使用 png
  }
}
