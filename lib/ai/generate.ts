const getSize = (prompt: string) => {
  const match = prompt.match(/<image size: (\d+)x(\d+)>$/i);
  if (match) {
    return `${match[1]}x${match[2]}`;
  }
  return null;
};

export async function generateImageRaw(promptMessages: any, modelId: string) {
  console.log("[DEBUG] Starting generateImageRaw with promptMessages:",
    JSON.stringify({
      hasImageFiles: !!promptMessages[0].imageFiles,
      imageFilesCount: promptMessages[0].imageFiles?.length || 0,
      content: promptMessages[0].content,
      hasExperimentalAttachments: !!promptMessages[0].experimental_attachments,
      experimentalAttachmentsCount: promptMessages[0].experimental_attachments?.length || 0
    }, null, 2)
  );

  const isOpenai = modelId === "draw-model-raw-openai"

  const method = "POST";
  const Authorization = `Bearer ${isOpenai ? process.env.OPENAI_API_KEY || process.env.TUZI_API_KEY : process.env.TUZI_OPENAI_API_KEY }`;
  const urlBase = isOpenai
    ? process.env.OPENAI_API_URL || process.env.TUZI_API_URL
    : process.env.TUZI_OPENAI_API_URL;
  const model = isOpenai
    ? process.env.OPENAI_MODEL_IMAGE || 'gpt-image-1'
    : process.env.TUZI_OPENAI_MODEL_IMAGE;

  // Check both imageFiles and experimental_attachments
  const attachments = promptMessages[0].imageFiles;
  const prompt = promptMessages[0].content;

  const moderation = "low";
  const quality = "high";
  const size = getSize(prompt);
  const n = 1;

  console.log(`[DEBUG] Using model: ${model}, size: ${size}, attachments count: ${attachments.length}`);

  let response: any;

  if (attachments && attachments.length > 0) {
    const bodyParams = new FormData();

    if (model) bodyParams.append("model", model);
    if (prompt) bodyParams.append("prompt", prompt);
    if (size) bodyParams.append("size", size);
    if (moderation) bodyParams.append("moderation", moderation);
    if (quality) bodyParams.append("quality", quality);
    if (n) bodyParams.append("n", n.toString());

    // Add attachments to the request body
    for (const attachment of attachments) {
      bodyParams.append("image[]", attachment);
    }

    // Log the request for debugging
    console.log(
      `[DEBUG] Sending request to ${urlBase}/images/edits with model: ${model}`
    );

    // Use /images/edits with JSON body for gpt-image-1
    response = await fetch(`${urlBase}/images/edits`, {
      headers: {
        Authorization,
      },
      method,
      body: bodyParams,
    });
  } else {
    response = await fetch(`${urlBase}/images/generations`, {
      headers: {
        Authorization,
        "Content-Type": "application/json",
      },
      method,
      body: JSON.stringify({
        model,
        prompt,
        size,
        quality,
        moderation,
        n,
      }),
    });
  }


  console.log(`[DEBUG] Response status: ${response.status} ${response.statusText}`);

  // Log response headers for debugging
  console.log("[DEBUG] Response headers:");
  response.headers.forEach((value: string, name: string) => {
    console.log(`[DEBUG] ${name}: ${value}`);
  });

  // 只读取一次响应体
  let responseData: any;
  let responseText: string = '';

  try {
    // 克隆响应以避免"Body has already been read"错误
    const responseClone = response.clone();

    try {
      // 尝试解析为JSON
      responseData = await response.json();
      console.log("[DEBUG] Response parsed as JSON successfully");
    } catch (jsonError) {
      // 如果JSON解析失败，尝试读取为文本
      console.error("[ERROR] Failed to parse response as JSON:", jsonError);
      responseText = await responseClone.text();
      console.log("[DEBUG] Response read as text:", responseText.substring(0, 200) + (responseText.length > 200 ? '...' : ''));
    }
  } catch (error) {
    console.error("[ERROR] Failed to read response:", error);
    throw new Error(`Failed to read API response: ${error instanceof Error ? error.message : String(error)}`);
  }

  if (!response.ok) {
    // 处理错误情况
    if (responseData) {
      console.error("[ERROR] API Error Response:", JSON.stringify(responseData, null, 2));
      throw new Error(`HTTP error! status: ${response.status}, message: ${JSON.stringify(responseData)}`);
    } else {
      console.error("[ERROR] API Error Text:", responseText);
      throw new Error(`HTTP error! status: ${response.status}, response: ${responseText}`);
    }
  }

  // 处理成功情况
  if (responseData) {
    console.log("[DEBUG] Successful response data:", responseData);
    return responseData;
  } else {
    console.error("[ERROR] No valid response data");
    throw new Error("Failed to get valid response data from API");
  }
}

export async function generateImageFlux(promptMessages: any, modelId: string) {
  console.log("[DEBUG] Starting generateImageFlux with promptMessages:",
    JSON.stringify({
      hasImageFiles: !!promptMessages[0].imageFiles,
      imageFilesCount: promptMessages[0].imageFiles?.length || 0,
      content: promptMessages[0].content,
      hasExperimentalAttachments: !!promptMessages[0].experimental_attachments,
      experimentalAttachmentsCount: promptMessages[0].experimental_attachments?.length || 0,
      modelId: modelId
    }, null, 2)
  );

  const method = "POST";

  const Authorization = `Bearer ${
    process.env.TUZI_API_KEY
  }`;
  const urlBase = process.env.TUZI_API_URL;
  const model = modelId === "draw-model-flux-max" ? process.env.TUZI_MODEL_FLUX_MAX : process.env.TUZI_MODEL_FLUX_PRO;

  // Check both imageFiles and experimental_attachments
  const attachments = promptMessages[0].imageFiles;
  const prompt = promptMessages[0].content;

  const moderation = "low";
  const quality = "high";
  const size = getSize(prompt);
  const n = 1;

  console.log(`[DEBUG] Using Flux model: ${model}, size: ${size}, attachments count: ${attachments?.length || 0}`);

  // Flux 模型只使用 /images/generations 端点，不支持 edits
  const response = await fetch(`${urlBase}/images/generations`, {
    headers: {
      Authorization,
      "Content-Type": "application/json",
    },
    method,
    body: JSON.stringify({
      model,
      prompt,
      size,
      quality,
      moderation,
      n,
    }),
  });

  console.log(`[DEBUG] Flux Response status: ${response.status} ${response.statusText}`);

  // Log response headers for debugging
  console.log("[DEBUG] Flux Response headers:");
  response.headers.forEach((value: string, name: string) => {
    console.log(`[DEBUG] ${name}: ${value}`);
  });

  // 只读取一次响应体
  let responseData: any;
  let responseText: string = '';

  try {
    // 克隆响应以避免"Body has already been read"错误
    const responseClone = response.clone();

    try {
      // 尝试解析为JSON
      responseData = await response.json();
      console.log("[DEBUG] Flux Response parsed as JSON successfully");
    } catch (jsonError) {
      // 如果JSON解析失败，尝试读取为文本
      console.error("[ERROR] Failed to parse Flux response as JSON:", jsonError);
      responseText = await responseClone.text();
      console.log("[DEBUG] Flux Response read as text:", responseText.substring(0, 200) + (responseText.length > 200 ? '...' : ''));
    }
  } catch (error) {
    console.error("[ERROR] Failed to read Flux response:", error);
    throw new Error(`Failed to read Flux API response: ${error instanceof Error ? error.message : String(error)}`);
  }

  if (!response.ok) {
    // 处理错误情况
    if (responseData) {
      console.error("[ERROR] Flux API Error Response:", JSON.stringify(responseData, null, 2));
      throw new Error(`Flux HTTP error! status: ${response.status}, message: ${JSON.stringify(responseData)}`);
    } else {
      console.error("[ERROR] Flux API Error Text:", responseText);
      throw new Error(`Flux HTTP error! status: ${response.status}, response: ${responseText}`);
    }
  }

  // 处理成功情况
  if (responseData) {
    console.log("[DEBUG] Successful Flux response data:", responseData);
    return responseData;
  } else {
    console.error("[ERROR] No valid Flux response data");
    throw new Error("Failed to get valid response data from Flux API");
  }
}
