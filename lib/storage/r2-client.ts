import { S3<PERSON>lient, PutO<PERSON>Command, GetO<PERSON>Command, DeleteObjectCommand, ListObjectsV2Command } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { Readable } from 'stream';
import path from 'path';
import { createLogger } from '@/lib/draw/logger';

// Create a logger for R2 operations
const logger = createLogger('r2-client');

// R2 client configuration
const R2_ACCOUNT_ID = process.env.R2_ACCOUNT_ID;
const R2_ACCESS_KEY_ID = process.env.R2_ACCESS_KEY_ID;
const R2_SECRET_ACCESS_KEY = process.env.R2_SECRET_ACCESS_KEY;
const R2_BUCKET_NAME = process.env.R2_BUCKET_NAME;
const R2_PUBLIC_URL_PREFIX = process.env.R2_PUBLIC_URL_PREFIX;

// Check if all required environment variables are set
if (!R2_ACCOUNT_ID || !R2_ACCESS_KEY_ID || !R2_SECRET_ACCESS_KEY || !R2_BUCKET_NAME || !R2_PUBLIC_URL_PREFIX) {
  logger.error('Missing R2 configuration environment variables', new Error('R2 configuration incomplete'));
}

// Create S3 client for R2
const s3Client = new S3Client({
  region: 'auto',
  endpoint: `https://${R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  credentials: {
    accessKeyId: R2_ACCESS_KEY_ID || '',
    secretAccessKey: R2_SECRET_ACCESS_KEY || '',
  },
});

/**
 * Extract filename from URL
 * @param url URL to extract filename from
 * @returns Filename
 */
export function extractFilenameFromUrl(url: string): string {
  try {
    // Parse the URL to get the pathname
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;

    // Get the filename from the pathname
    const filename = path.basename(pathname);

    // If filename has no extension, generate a random one
    if (!path.extname(filename)) {
      return `image-${Date.now()}.jpg`;
    }

    return filename;
  } catch (error) {
    const err = error instanceof Error ? error : new Error('Failed to extract filename');
    logger.error('Error extracting filename from URL', err, { url, details: error });
    return `image-${Date.now()}.jpg`;
  }
}

/**
 * Check if a URL is accessible
 * @param url URL to check
 * @returns True if URL is accessible, false otherwise
 */
export async function isUrlAccessible(url: string): Promise<boolean> {
  try {
    // Try to fetch the URL with HEAD request to check if it's accessible
    const response = await fetch(url, { method: 'HEAD' });
    return response.ok;
  } catch (error) {
    const err = error instanceof Error ? error : new Error('Failed to check URL accessibility');
    logger.error('Error checking URL accessibility', err, { url, details: error });
    return false;
  }
}

/**
 * Upload a file to R2 from a URL using streaming (no local download)
 * @param url Source URL
 * @param key R2 object key (path)
 * @returns Public URL of the uploaded file or null if failed
 */
export async function streamUploadFromUrl(url: string, key: string): Promise<string | null> {
  try {
    // Check if URL has an image extension
    const hasImageExtension = /\.(jpg|jpeg|png|gif|webp)(?:\?|#|$)/i.test(url);

    // If no image extension, check if URL is accessible before proceeding
    if (!hasImageExtension) {
      const isAccessible = await isUrlAccessible(url);
      if (!isAccessible) {
        logger.error(
          'URL is not accessible and does not have an image extension',
          new Error('URL validation failed'),
          { url, key }
        );
        return null;
      }
    }

    // Fetch the file from the URL
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`Failed to fetch file from URL: ${response.status} ${response.statusText}`);
    }

    // Get the content type
    const contentType = response.headers.get('content-type') || 'application/octet-stream';

    // Check if content type is an image
    if (!contentType.startsWith('image/')) {
      logger.error(
        'URL does not point to an image',
        new Error(`Invalid content type: ${contentType}`),
        { url, key, contentType }
      );
      return null;
    }

    // Stream the response body directly to R2
    await s3Client.send(
      new PutObjectCommand({
        Bucket: R2_BUCKET_NAME,
        Key: key,
        Body: Buffer.from(await response.arrayBuffer()), // Convert ArrayBuffer to Buffer for S3 client
        ContentType: contentType,
      })
    );

    // Return the public URL
    return `${R2_PUBLIC_URL_PREFIX}/${key}`;
  } catch (error) {
    const err = error instanceof Error ? error : new Error('Failed to upload file');
    logger.error('Error streaming upload to R2', err, { url, key, details: error });
    return null;
  }
}

/**
 * Upload a file to R2 from a URL
 * @param url Source URL
 * @param key R2 object key (path)
 * @returns Public URL of the uploaded file
 * @deprecated Use streamUploadFromUrl instead for better performance
 */
export async function uploadFileFromUrl(url: string, key: string): Promise<string> {
  try {
    // Fetch the file from the URL
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`Failed to fetch file from URL: ${response.status} ${response.statusText}`);
    }

    // Get the file buffer
    const buffer = await response.arrayBuffer();

    // Upload the file to R2
    await s3Client.send(
      new PutObjectCommand({
        Bucket: R2_BUCKET_NAME,
        Key: key,
        Body: Buffer.from(buffer),
        ContentType: response.headers.get('content-type') || 'application/octet-stream',
      })
    );

    // Return the public URL
    return `${R2_PUBLIC_URL_PREFIX}/${key}`;
  } catch (error) {
    const err = error instanceof Error ? error : new Error('Failed to upload file');
    logger.error('Error uploading file to R2', err, { url, key, details: error });
    throw error;
  }
}

/**
 * Upload a buffer to R2
 * @param buffer File buffer
 * @param key R2 object key (path)
 * @param contentType Content type of the file
 * @returns Public URL of the uploaded file
 */
export async function uploadBuffer(buffer: Buffer, key: string, contentType: string = 'application/octet-stream'): Promise<string> {
  try {
    // Upload the buffer to R2
    await s3Client.send(
      new PutObjectCommand({
        Bucket: R2_BUCKET_NAME,
        Key: key,
        Body: buffer,
        ContentType: contentType,
      })
    );

    // Return the public URL
    return `${R2_PUBLIC_URL_PREFIX}/${key}`;
  } catch (error) {
    const err = error instanceof Error ? error : new Error('Failed to upload buffer');
    logger.error('Error uploading buffer to R2', err, { key, details: error });
    throw error;
  }
}

/**
 * Get a signed URL for an object in R2
 * @param key R2 object key (path)
 * @param expiresIn Expiration time in seconds (default: 3600)
 * @returns Signed URL
 */
export async function getSignedUrlForObject(key: string, expiresIn: number = 3600): Promise<string> {
  try {
    const command = new GetObjectCommand({
      Bucket: R2_BUCKET_NAME,
      Key: key,
    });

    return await getSignedUrl(s3Client, command, { expiresIn });
  } catch (error) {
    const err = error instanceof Error ? error : new Error('Failed to generate signed URL');
    logger.error('Error generating signed URL', err, { key, details: error });
    throw error;
  }
}

/**
 * Get the public URL for an object in R2
 * @param key R2 object key (path)
 * @returns Public URL
 */
export function getPublicUrl(key: string): string {
  return `${R2_PUBLIC_URL_PREFIX}/${key}`;
}

/**
 * Upload a log file to R2
 * @param content Log content
 * @param filename Log filename
 * @returns Public URL of the log file
 */
export async function uploadLogFile(content: string, filename: string): Promise<string> {
  try {
    const key = `logs/${filename}`;

    // Upload the log file to R2
    await s3Client.send(
      new PutObjectCommand({
        Bucket: R2_BUCKET_NAME,
        Key: key,
        Body: content,
        ContentType: 'text/plain',
      })
    );

    // Return the public URL
    return getPublicUrl(key);
  } catch (error) {
    const err = error instanceof Error ? error : new Error('Failed to upload log file');
    logger.error('Error uploading log file to R2', err, { filename, details: error });
    throw error;
  }
}

/**
 * Delete an object from R2
 * @param key R2 object key (path)
 * @returns True if deletion was successful, false otherwise
 */
export async function deleteObjectFromR2(key: string): Promise<boolean> {
  try {
    // Delete the object from R2
    await s3Client.send(
      new DeleteObjectCommand({
        Bucket: R2_BUCKET_NAME,
        Key: key,
      })
    );

    logger.info('Object deleted from R2', { key });
    return true;
  } catch (error) {
    const err = error instanceof Error ? error : new Error('Failed to delete object');
    logger.error('Error deleting object from R2', err, { key, details: error });
    return false;
  }
}

/**
 * List objects in R2 with a prefix
 * @param prefix Prefix to filter objects by
 * @returns Array of object keys
 */
export async function listObjectsWithPrefix(prefix: string): Promise<string[]> {
  try {
    const command = new ListObjectsV2Command({
      Bucket: R2_BUCKET_NAME,
      Prefix: prefix,
    });

    const response = await s3Client.send(command);
    const keys = response.Contents?.map(obj => obj.Key || '') || [];

    return keys.filter(key => key !== '');
  } catch (error) {
    const err = error instanceof Error ? error : new Error('Failed to list objects');
    logger.error('Error listing objects from R2', err, { prefix, details: error });
    return [];
  }
}

/**
 * Delete all objects in R2 with a prefix
 * @param prefix Prefix to filter objects by
 * @returns Number of objects deleted
 */
export async function deleteObjectsWithPrefix(prefix: string): Promise<number> {
  try {
    // List all objects with the prefix
    const keys = await listObjectsWithPrefix(prefix);

    if (keys.length === 0) {
      logger.info('No objects found with prefix', { prefix });
      return 0;
    }

    // Delete each object
    let deletedCount = 0;
    for (const key of keys) {
      const success = await deleteObjectFromR2(key);
      if (success) {
        deletedCount++;
      }
    }

    logger.info(`Deleted ${deletedCount} objects with prefix ${prefix}`, { prefix, totalObjects: keys.length, deletedCount });
    return deletedCount;
  } catch (error) {
    const err = error instanceof Error ? error : new Error('Failed to delete objects with prefix');
    logger.error('Error deleting objects with prefix from R2', err, { prefix, details: error });
    return 0;
  }
}
