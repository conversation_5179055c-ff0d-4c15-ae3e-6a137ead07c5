# Flux 模型图像生成实现文档

## 概述

本文档记录了 Flux 模型（flux-kontext-pro 和 flux-kontext-max）的图像生成功能实现。Flux 模型是一种新的图像生成模型，具有与现有 image 类型模型不同的 API 调用方式和响应格式。

## 模型配置

在 `constants/draw/models.ts` 中定义了两个 Flux 模型：

```typescript
{
  id: "draw-model-flux-pro",
  type: "flux",
  name: "Flux Kontext",
  description: "基于全新 flux-kontext-pro 模型",
  paidOnly: true,
  disabled: false,
  points: 50,
  maxImages: 1,
},
{
  id: "draw-model-flux-max",
  type: "flux",
  name: "Flux Kontext 高级版",
  description: "基于全新 flux-kontext-max 模型",
  paidOnly: true,
  disabled: false,
  points: 70,
  maxImages: 1,
}
```

## API 特征

### 请求方式
- **端点**: 只调用 `{baseUrl}/images/generations` 接口
- **不支持**: 不调用 `edits` 接口（与 image 类型模型的主要区别）
- **参数处理**: 传入参数的处理方式与 `generateImageRaw` 相同

### 响应格式
Flux 模型的 API 响应格式与 image 类型模型不同：

```json
{
  "data": [
    {
      "url": "https://fal.media/files/zebra/MyOd-G64uQKxkAhR0xcg-_fbd4c9777f6c4132a75f74366e0dcf2e.png"
    }
  ],
  "created": 1748657635
}
```

**关键差异**:
- 返回的是 `url` 字段而不是 `b64_json` 字段
- 图像以 URL 形式提供，而不是 base64 编码

## 实现方案

### 1. 新增 generateImageFlux 函数

在 `lib/ai/generate.ts` 中新增专门处理 Flux 模型的函数：

```typescript
export async function generateImageFlux(promptMessages: any, modelId: string) {
  // 参数处理逻辑与 generateImageRaw 相同
  // 但只调用 /images/generations 端点
  // 处理返回的 URL 格式响应
}
```

### 2. 修改调用逻辑

在 `lib/draw/image-requests.ts` 的 `processDrawRequest` 函数中添加对 Flux 模型的处理：

```typescript
if (model.type === "flux") {
  // 使用 generateImageFlux
  result = await generateImageFlux(promptMessages, modelId);
} else if (model.type === "image") {
  // 使用 generateImageRaw
  result = await generateImageRaw(promptMessages, modelId);
}
```

### 3. 响应处理

由于 Flux 模型返回的是 URL 而不是 base64 数据，需要在响应处理部分添加相应逻辑：

```typescript
if (model.type === "flux") {
  // 处理 URL 格式的响应
  const imageUrl = result.data[0].url;
  text = imageUrl; // 或进行进一步处理
}
```

## 环境变量

Flux 模型使用与现有模型相同的环境变量配置：
- `TUZI_OPENAI_API_KEY`: API 密钥
- `TUZI_OPENAI_API_URL`: API 基础 URL
- `TUZI_OPENAI_MODEL_IMAGE`: 模型名称

## 实现步骤

1. **创建 generateImageFlux 函数**
   - 复制 generateImageRaw 的基础结构
   - 移除 edits 端点的调用逻辑
   - 只保留 generations 端点的调用
   - 适配 URL 格式的响应处理

2. **修改模型调用逻辑**
   - 在 `processDrawRequest` 中添加 flux 类型的判断
   - 调用相应的生成函数

3. **适配响应处理**
   - 处理 URL 格式的图像响应
   - 确保与现有的图像处理流程兼容

## 注意事项

1. **单一端点**: Flux 模型只使用 generations 端点，不支持 edits
2. **响应格式**: 返回 URL 而不是 base64 数据
3. **图像限制**: 当前配置为每次最多 1 张图像
4. **付费模型**: 两个 Flux 模型都是付费模型

## 实现完成状态

### ✅ 已完成的功能

1. **generateImageFlux 函数** - 在 `lib/ai/generate.ts` 中实现
   - 复制了 generateImageRaw 的基础结构
   - 移除了 edits 端点的调用逻辑
   - 只保留 generations 端点的调用
   - 适配了 URL 格式的响应处理

2. **模型调用逻辑** - 在 `lib/draw/image-requests.ts` 中修改
   - 添加了对 flux 类型的判断
   - 调用相应的 generateImageFlux 函数

3. **响应处理** - 在 `lib/draw/image-requests.ts` 中适配
   - 处理 URL 格式的图像响应
   - 确保与现有的图像处理流程兼容
   - 添加了 R2 上传失败时的回退逻辑

### 🔧 实现细节

#### generateImageFlux 函数特点
- 使用与 generateImageRaw 相同的环境变量配置
- 只调用 `/images/generations` 端点，不支持 edits
- 返回包含 URL 的响应格式：`{"data":[{"url":"..."}],"created":...}`
- 添加了详细的调试日志，便于问题排查

#### 响应处理逻辑
```typescript
if (model.type === "flux") {
  // Flux 模型返回 URL 格式
  imageObj = result.data[0];
  const imageUrl = imageObj.url;
  text = imageUrl;
}
```

#### R2 存储处理
- 将 Flux 模型包含在需要 R2 存储的模型类型中
- 在上传失败时，Flux 模型直接使用原始 URL 作为回退方案

## 测试要点

1. 验证 Flux 模型的选择和调用
2. 确认只调用 generations 端点
3. 验证 URL 格式响应的正确处理
4. 测试与现有图像处理流程的兼容性
5. 测试 R2 存储的备份功能
6. 验证积分扣除逻辑的正确性

## 使用说明

1. **选择模型**: 在前端选择 "Flux Kontext" 或 "Flux Kontext 高级版"
2. **输入提示词**: 输入图像生成的描述文字
3. **生成图像**: 系统将调用 generateImageFlux 函数
4. **获取结果**: 系统返回生成的图像 URL

## 注意事项

1. **环境变量**: 确保 `TUZI_OPENAI_API_KEY`、`TUZI_OPENAI_API_URL` 和 `TUZI_OPENAI_MODEL_IMAGE` 已正确配置
2. **网络访问**: Flux 模型返回的 URL 需要确保用户可以正常访问
3. **备份策略**: 建议为 Flux 模型的图像 URL 实现备份机制，防止原始 URL 失效
